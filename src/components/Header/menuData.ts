import { Menu } from "@/types/Menu";

export const menuData: Menu[] = [
  {
    id: 1,
    title: "首页",
    newTab: false,
    path: "/",
  },
  {
    id: 2,
    title: "服务",
    newTab: false,
    path: "/services-with-sidebar",
  },
  {
    id: 3,
    title: "关于我们",
    newTab: false,
    path: "/about",
  },
  {
    id: 4,
    title: "联系我们",
    newTab: false,
    path: "/contact",
  },
  {
    id: 6,
    title: "页面",
    newTab: false,
    path: "/",
    submenu: [
      {
        id: 61,
        title: "服务展示",
        newTab: false,
        path: "/services-with-sidebar",
      },
      {
        id: 62,
        title: "服务列表",
        newTab: false,
        path: "/services-without-sidebar",
      },
      {
        id: 70,
        title: "联系我们",
        newTab: false,
        path: "/contact",
      },
      {
        id: 62,
        title: "错误页面",
        newTab: false,
        path: "/error",
      },
      {
        id: 63,
        title: "邮件成功",
        newTab: false,
        path: "/mail-success",
      },
    ],
  },
  {
    id: 7,
    title: "新闻",
    newTab: false,
    path: "/",
    submenu: [
      {
        id: 71,
        title: "新闻网格（带侧栏）",
        newTab: false,
        path: "/blogs/blog-grid-with-sidebar",
      },
      {
        id: 72,
        title: "新闻网格",
        newTab: false,
        path: "/blogs/blog-grid",
      },
      {
        id: 73,
        title: "新闻详情（带侧栏）",
        newTab: false,
        path: "/blogs/blog-details-with-sidebar",
      },
      {
        id: 74,
        title: "新闻详情",
        newTab: false,
        path: "/blogs/blog-details",
      },
    ],
  },
];
