import React from "react";
import Breadcrumb from "@/components/Common/Breadcrumb";

const AboutPage = () => {
  return (
    <>
      <Breadcrumb
        title={"关于我们"}
        pages={["首页", "关于我们"]}
      />
      
      <section className="overflow-hidden pt-15 pb-20">
        <div className="max-w-[1170px] w-full mx-auto px-4 sm:px-8 xl:px-0">
          <div className="text-center mb-15">
            <h2 className="font-semibold text-2xl xl:text-heading-3 text-dark mb-6">
              专业物流服务提供商
            </h2>
            <p className="text-lg text-dark-4 max-w-[600px] mx-auto">
              我们致力于为客户提供安全、快速、可靠的物流解决方案，覆盖国内外运输服务。
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-white shadow-1 rounded-lg p-6">
              <div className="w-12 h-12 bg-blue rounded-lg flex items-center justify-center mb-4">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="font-semibold text-lg text-dark mb-3">快速配送</h3>
              <p className="text-dark-4">
                提供24小时快速配送服务，确保货物及时送达目的地。
              </p>
            </div>

            <div className="bg-white shadow-1 rounded-lg p-6">
              <div className="w-12 h-12 bg-blue rounded-lg flex items-center justify-center mb-4">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="font-semibold text-lg text-dark mb-3">安全保障</h3>
              <p className="text-dark-4">
                严格的包装和运输标准，确保货物在运输过程中的安全。
              </p>
            </div>

            <div className="bg-white shadow-1 rounded-lg p-6">
              <div className="w-12 h-12 bg-blue rounded-lg flex items-center justify-center mb-4">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064" />
                </svg>
              </div>
              <h3 className="font-semibold text-lg text-dark mb-3">全球网络</h3>
              <p className="text-dark-4">
                覆盖全球主要城市的物流网络，提供国际运输服务。
              </p>
            </div>
          </div>

          <div className="mt-15 bg-gray-1 rounded-lg p-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
              <div>
                <h3 className="font-semibold text-xl text-dark mb-4">
                  我们的承诺
                </h3>
                <p className="text-dark-4 mb-4">
                  作为专业的物流服务提供商，我们始终以客户需求为中心，
                  提供定制化的物流解决方案。
                </p>
                <ul className="space-y-2">
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue rounded-full"></div>
                    <span className="text-dark-4">7×24小时客户服务</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue rounded-full"></div>
                    <span className="text-dark-4">实时货物跟踪</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue rounded-full"></div>
                    <span className="text-dark-4">专业团队支持</span>
                  </li>
                </ul>
              </div>
              
              <div className="text-center">
                <div className="grid grid-cols-2 gap-6">
                  <div>
                    <div className="text-3xl font-bold text-blue">1000+</div>
                    <p className="text-dark-4">服务客户</p>
                  </div>
                  <div>
                    <div className="text-3xl font-bold text-blue">50+</div>
                    <p className="text-dark-4">服务城市</p>
                  </div>
                  <div>
                    <div className="text-3xl font-bold text-blue">99.8%</div>
                    <p className="text-dark-4">准时率</p>
                  </div>
                  <div>
                    <div className="text-3xl font-bold text-blue">24/7</div>
                    <p className="text-dark-4">客户服务</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default AboutPage;